import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import GroupManagementModal from '../GroupManagementModal';

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

describe('GroupManagementModal - 批量操作功能', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该显示多选功能', async () => {
    render(<GroupManagementModal visible={true} onCancel={() => {}} />);

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('分组管理')).toBeInTheDocument();
    });

    // 检查是否有复选框
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes.length).toBeGreaterThan(0);
  });

  test('应该支持跨页多选', async () => {
    render(<GroupManagementModal visible={true} onCancel={() => {}} />);

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('分组管理')).toBeInTheDocument();
    });

    // 选择第一个项目
    const firstCheckbox = screen.getAllByRole('checkbox')[1]; // 跳过全选框
    fireEvent.click(firstCheckbox);

    // 应该显示选择提示栏
    await waitFor(() => {
      expect(screen.getByText(/已选择/)).toBeInTheDocument();
      expect(screen.getByText('批量删除')).toBeInTheDocument();
      expect(screen.getByText('取消选择')).toBeInTheDocument();
    });
  });

  test('应该能够取消选择', async () => {
    render(<GroupManagementModal visible={true} onCancel={() => {}} />);

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('分组管理')).toBeInTheDocument();
    });

    // 选择第一个项目
    const firstCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstCheckbox);

    // 等待选择提示栏出现
    await waitFor(() => {
      expect(screen.getByText('取消选择')).toBeInTheDocument();
    });

    // 点击取消选择
    fireEvent.click(screen.getByText('取消选择'));

    // 选择提示栏应该消失
    await waitFor(() => {
      expect(screen.queryByText(/已选择/)).not.toBeInTheDocument();
    });
  });

  test('应该能够批量删除', async () => {
    render(<GroupManagementModal visible={true} onCancel={() => {}} />);

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('分组管理')).toBeInTheDocument();
    });

    // 选择第一个项目
    const firstCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstCheckbox);

    // 等待选择提示栏出现
    await waitFor(() => {
      expect(screen.getByText('批量删除')).toBeInTheDocument();
    });

    // 点击批量删除
    fireEvent.click(screen.getByText('批量删除'));

    // 应该显示确认对话框
    await waitFor(() => {
      expect(screen.getByText('确认批量删除')).toBeInTheDocument();
    });

    // 点击确认
    fireEvent.click(screen.getByText('确认'));

    // 应该显示成功消息
    await waitFor(() => {
      expect(message.success).toHaveBeenCalledWith(expect.stringContaining('成功删除'));
    });
  });

  test('搜索时应该清空选择状态', async () => {
    render(<GroupManagementModal visible={true} onCancel={() => {}} />);

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('分组管理')).toBeInTheDocument();
    });

    // 选择第一个项目
    const firstCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstCheckbox);

    // 等待选择提示栏出现
    await waitFor(() => {
      expect(screen.getByText(/已选择/)).toBeInTheDocument();
    });

    // 进行搜索
    const searchInput = screen.getByPlaceholderText('请输入分组名称');
    fireEvent.change(searchInput, { target: { value: '数据' } });
    fireEvent.click(screen.getByText('搜索'));

    // 选择提示栏应该消失
    await waitFor(() => {
      expect(screen.queryByText(/已选择/)).not.toBeInTheDocument();
    });
  });
});
