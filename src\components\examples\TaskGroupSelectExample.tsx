import React, { useState } from 'react';
import { Card, Form, Button, Space, Divider, message } from 'antd';
import { TaskGroupSelect } from '../common/TaskGroupSelect';

/**
 * TaskGroupSelect 组件使用示例
 * 展示了各种使用场景和配置选项
 */
const TaskGroupSelectExample: React.FC = () => {
  const [selectedGroup1, setSelectedGroup1] = useState<string>();
  const [selectedGroup2, setSelectedGroup2] = useState<string>();
  const [selectedGroup3, setSelectedGroup3] = useState<string>();
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    console.log('表单提交数据:', values);
    message.success('表单提交成功！');
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">TaskGroupSelect 组件使用示例</h1>
      
      {/* 基础用法 */}
      <Card title="1. 基础用法" className="mb-6">
        <Space direction="vertical" className="w-full">
          <div>
            <label className="block mb-2 font-medium">选择任务分组：</label>
            <TaskGroupSelect 
              value={selectedGroup1}
              onChange={setSelectedGroup1}
              placeholder="请选择任务分组"
            />
          </div>
          <div className="text-gray-600">
            当前选中：{selectedGroup1 || '未选择'}
          </div>
        </Space>
      </Card>

      {/* 不同尺寸和样式 */}
      <Card title="2. 不同尺寸和样式" className="mb-6">
        <Space direction="vertical" className="w-full" size="large">
          <div>
            <label className="block mb-2 font-medium">小尺寸：</label>
            <TaskGroupSelect 
              size="small"
              placeholder="小尺寸选择框"
              className="w-64"
            />
          </div>
          <div>
            <label className="block mb-2 font-medium">中等尺寸（默认）：</label>
            <TaskGroupSelect 
              size="middle"
              placeholder="中等尺寸选择框"
              className="w-64"
            />
          </div>
          <div>
            <label className="block mb-2 font-medium">大尺寸：</label>
            <TaskGroupSelect 
              size="large"
              placeholder="大尺寸选择框"
              className="w-64"
            />
          </div>
        </Space>
      </Card>

      {/* 过滤选项 */}
      <Card title="3. 按使用状态过滤" className="mb-6">
        <Space direction="vertical" className="w-full" size="large">
          <div>
            <label className="block mb-2 font-medium">只显示已使用的分组：</label>
            <TaskGroupSelect 
              onlyUsed={true}
              placeholder="请选择已使用的分组"
              value={selectedGroup2}
              onChange={setSelectedGroup2}
            />
          </div>
          <div>
            <label className="block mb-2 font-medium">只显示未使用的分组：</label>
            <TaskGroupSelect 
              onlyUnused={true}
              placeholder="请选择未使用的分组"
              value={selectedGroup3}
              onChange={setSelectedGroup3}
            />
          </div>
        </Space>
      </Card>

      {/* 表单中使用 */}
      <Card title="4. 在表单中使用" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            taskName: '',
            taskGroup: '',
            description: '',
          }}
        >
          <Form.Item
            name="taskName"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <input 
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务名称"
            />
          </Form.Item>

          <Form.Item
            name="taskGroup"
            label="任务分组"
            rules={[{ required: true, message: '请选择任务分组' }]}
          >
            <TaskGroupSelect placeholder="请选择任务分组" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <textarea 
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务描述"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交
              </Button>
              <Button onClick={() => form.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 禁用状态 */}
      <Card title="5. 禁用状态" className="mb-6">
        <Space direction="vertical" className="w-full">
          <div>
            <label className="block mb-2 font-medium">禁用状态：</label>
            <TaskGroupSelect 
              disabled={true}
              placeholder="禁用状态的选择框"
              value="示例分组"
            />
          </div>
        </Space>
      </Card>

      {/* 自定义样式 */}
      <Card title="6. 自定义样式" className="mb-6">
        <Space direction="vertical" className="w-full">
          <div>
            <label className="block mb-2 font-medium">自定义样式：</label>
            <TaskGroupSelect 
              placeholder="自定义样式的选择框"
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              className="w-80"
            />
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default TaskGroupSelectExample;
