import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import { TaskGroupSelect } from '../common/TaskGroupSelect';
import { TaskService } from '../../services/taskService';

// Mock TaskService
jest.mock('../../services/taskService');
const mockTaskService = TaskService as jest.Mocked<typeof TaskService>;

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    error: jest.fn(),
  },
}));

describe('TaskGroupSelect', () => {
  const mockGroups = [
    { id: 1, name: '测试分组1', is_used: true, create_time: '2024-01-01', update_time: '2024-01-01' },
    { id: 2, name: '测试分组2', is_used: false, create_time: '2024-01-01', update_time: '2024-01-01' },
    { id: 3, name: '测试分组3', is_used: true, create_time: '2024-01-01', update_time: '2024-01-01' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockTaskService.getTaskGroups.mockResolvedValue({
      data: mockGroups,
      total: mockGroups.length,
      success: true,
    });
  });

  it('应该正确渲染组件', async () => {
    render(<TaskGroupSelect />);
    
    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByText('请选择任务分组')).toBeInTheDocument();
  });

  it('应该加载并显示分组选项', async () => {
    render(<TaskGroupSelect />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(mockTaskService.getTaskGroups).toHaveBeenCalledWith({});
    });

    // 点击选择框打开下拉菜单
    fireEvent.mouseDown(screen.getByRole('combobox'));
    
    // 验证选项是否正确显示
    await waitFor(() => {
      expect(screen.getByText('测试分组1')).toBeInTheDocument();
      expect(screen.getByText('测试分组2')).toBeInTheDocument();
      expect(screen.getByText('测试分组3')).toBeInTheDocument();
    });
  });

  it('应该支持选择分组', async () => {
    const mockOnChange = jest.fn();
    render(<TaskGroupSelect onChange={mockOnChange} />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(mockTaskService.getTaskGroups).toHaveBeenCalled();
    });

    // 点击选择框
    fireEvent.mouseDown(screen.getByRole('combobox'));
    
    // 选择一个选项
    await waitFor(() => {
      fireEvent.click(screen.getByText('测试分组1'));
    });

    expect(mockOnChange).toHaveBeenCalledWith('测试分组1');
  });

  it('应该支持搜索功能', async () => {
    render(<TaskGroupSelect />);
    
    await waitFor(() => {
      expect(mockTaskService.getTaskGroups).toHaveBeenCalled();
    });

    // 点击选择框并输入搜索内容
    const selectInput = screen.getByRole('combobox');
    fireEvent.mouseDown(selectInput);
    fireEvent.change(selectInput, { target: { value: '分组1' } });

    // 验证搜索结果
    await waitFor(() => {
      expect(screen.getByText('测试分组1')).toBeInTheDocument();
    });
  });

  it('应该支持只显示已使用的分组', async () => {
    render(<TaskGroupSelect onlyUsed={true} />);
    
    await waitFor(() => {
      expect(mockTaskService.getTaskGroups).toHaveBeenCalledWith({
        is_used: true,
      });
    });
  });

  it('应该支持只显示未使用的分组', async () => {
    render(<TaskGroupSelect onlyUnused={true} />);
    
    await waitFor(() => {
      expect(mockTaskService.getTaskGroups).toHaveBeenCalledWith({
        is_used: false,
      });
    });
  });

  it('应该处理加载错误', async () => {
    const mockError = new Error('加载失败');
    mockTaskService.getTaskGroups.mockRejectedValue(mockError);

    render(<TaskGroupSelect />);
    
    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('加载任务分组失败: Error: 加载失败');
    });
  });

  it('应该支持禁用状态', () => {
    render(<TaskGroupSelect disabled={true} />);
    
    const selectElement = screen.getByRole('combobox');
    expect(selectElement).toHaveAttribute('aria-disabled', 'true');
  });

  it('应该支持自定义占位符', () => {
    const customPlaceholder = '自定义占位符';
    render(<TaskGroupSelect placeholder={customPlaceholder} />);
    
    expect(screen.getByText(customPlaceholder)).toBeInTheDocument();
  });

  it('应该支持清除选择', async () => {
    const mockOnChange = jest.fn();
    render(<TaskGroupSelect value="测试分组1" onChange={mockOnChange} allowClear={true} />);
    
    // 查找清除按钮并点击
    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);
    
    expect(mockOnChange).toHaveBeenCalledWith(undefined);
  });

  it('应该支持不同尺寸', () => {
    const { rerender } = render(<TaskGroupSelect size="small" />);
    expect(screen.getByRole('combobox')).toHaveClass('ant-select-sm');
    
    rerender(<TaskGroupSelect size="large" />);
    expect(screen.getByRole('combobox')).toHaveClass('ant-select-lg');
  });
});
