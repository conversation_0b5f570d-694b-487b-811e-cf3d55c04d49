import React, { useState } from 'react';
import { Card, Space, Button, message, Form } from 'antd';
import { TaskGroupSelect } from './common/TaskGroupSelect';

/**
 * TaskGroupSelect 组件演示页面
 * 展示如何使用任务分组选择组件
 */
const TaskGroupSelectDemo: React.FC = () => {
  const [selectedGroup, setSelectedGroup] = useState<string>();
  const [form] = Form.useForm();

  const handleGroupChange = (value: string) => {
    setSelectedGroup(value);
    message.info(`选择了分组: ${value}`);
  };

  const handleFormSubmit = (values: any) => {
    console.log('表单数据:', values);
    message.success(`提交成功！选择的分组: ${values.taskGroup}`);
  };

  const handleClear = () => {
    setSelectedGroup(undefined);
    message.info('已清除选择');
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">任务分组选择组件演示</h1>
      
      {/* 基础使用示例 */}
      <Card title="基础使用" className="mb-6">
        <Space direction="vertical" className="w-full" size="large">
          <div>
            <label className="block mb-2 font-medium text-gray-700">
              选择任务分组：
            </label>
            <TaskGroupSelect 
              value={selectedGroup}
              onChange={handleGroupChange}
              placeholder="请选择任务分组"
              className="w-full max-w-md rounded-md"
            />
          </div>
          
          <div className="p-3 bg-gray-50 rounded-md">
            <strong>当前选中的分组：</strong> 
            <span className="ml-2 text-blue-600">
              {selectedGroup || '未选择'}
            </span>
          </div>

          <Space>
            <Button onClick={handleClear}>
              清除选择
            </Button>
            <Button 
              type="primary" 
              disabled={!selectedGroup}
              onClick={() => message.success(`确认选择: ${selectedGroup}`)}
            >
              确认选择
            </Button>
          </Space>
        </Space>
      </Card>

      {/* 表单中使用示例 */}
      <Card title="在表单中使用" className="mb-6">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          className="max-w-md"
        >
          <Form.Item
            name="taskName"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <input 
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务名称"
            />
          </Form.Item>

          <Form.Item
            name="taskGroup"
            label="任务分组"
            rules={[{ required: true, message: '请选择任务分组' }]}
          >
            <TaskGroupSelect placeholder="请选择任务分组" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交表单
              </Button>
              <Button onClick={() => form.resetFields()}>
                重置表单
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 不同配置示例 */}
      <Card title="不同配置选项" className="mb-6">
        <Space direction="vertical" className="w-full" size="large">
          <div>
            <label className="block mb-2 font-medium text-gray-700">
              只显示已使用的分组：
            </label>
            <TaskGroupSelect 
              onlyUsed={true}
              placeholder="请选择已使用的分组"
              className="w-full max-w-md"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              只显示未使用的分组：
            </label>
            <TaskGroupSelect 
              onlyUnused={true}
              placeholder="请选择未使用的分组"
              className="w-full max-w-md"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              小尺寸选择框：
            </label>
            <TaskGroupSelect 
              size="small"
              placeholder="小尺寸选择框"
              className="w-64"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              大尺寸选择框：
            </label>
            <TaskGroupSelect 
              size="large"
              placeholder="大尺寸选择框"
              className="w-64"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              禁用状态：
            </label>
            <TaskGroupSelect 
              disabled={true}
              placeholder="禁用状态"
              value="示例分组"
              className="w-64"
            />
          </div>
        </Space>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明" className="mb-6">
        <div className="prose max-w-none">
          <h3>组件特性：</h3>
          <ul>
            <li>自动从 TaskService.getTaskGroups() 加载分组数据</li>
            <li>支持搜索过滤功能</li>
            <li>支持清除选择</li>
            <li>支持按使用状态过滤（onlyUsed, onlyUnused）</li>
            <li>支持不同尺寸（small, middle, large）</li>
            <li>支持禁用状态</li>
            <li>加载状态显示</li>
            <li>错误处理</li>
          </ul>

          <h3>基本用法：</h3>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`<TaskGroupSelect 
  value={selectedGroup}
  onChange={setSelectedGroup}
  placeholder="请选择任务分组"
/>`}
          </pre>

          <h3>在表单中使用：</h3>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`<Form.Item name="taskGroup" label="任务分组">
  <TaskGroupSelect placeholder="请选择任务分组" />
</Form.Item>`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default TaskGroupSelectDemo;
