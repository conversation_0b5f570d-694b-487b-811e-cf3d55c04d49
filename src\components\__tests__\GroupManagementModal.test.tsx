import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { App } from 'antd';
import GroupManagementModal from '../GroupManagementModal';

// Mock Ant Design App context
const MockApp: React.FC<{ children: React.ReactNode }> = ({ children }) => <App>{children}</App>;

describe('GroupManagementModal', () => {
  const defaultProps = {
    visible: true,
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染分组管理Modal', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    expect(screen.getByText('分组管理')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入分组名称')).toBeInTheDocument();
    expect(screen.getByText('搜索')).toBeInTheDocument();
    expect(screen.getByText('重置')).toBeInTheDocument();
    expect(screen.getByText('新增分组')).toBeInTheDocument();
  });

  it('应该显示表格列标题', async () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    await waitFor(() => {
      expect(screen.getByText('ID')).toBeInTheDocument();
      expect(screen.getByText('分组名称')).toBeInTheDocument();
      expect(screen.getByText('创建时间')).toBeInTheDocument();
      expect(screen.getByText('更新时间')).toBeInTheDocument();
      expect(screen.getByText('操作')).toBeInTheDocument();
    });
  });

  it('应该能够输入搜索文本', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    const searchInput = screen.getByPlaceholderText('请输入分组名称');
    fireEvent.change(searchInput, { target: { value: '数据同步' } });

    expect(searchInput).toHaveValue('数据同步');
  });

  it('应该能够点击搜索按钮', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    const searchButton = screen.getByText('搜索');
    fireEvent.click(searchButton);

    // 验证搜索功能被触发（通过检查是否有loading状态或其他副作用）
    expect(searchButton).toBeInTheDocument();
  });

  it('应该能够点击重置按钮', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    const searchInput = screen.getByPlaceholderText('请输入分组名称');
    fireEvent.change(searchInput, { target: { value: '测试' } });

    const resetButton = screen.getByText('重置');
    fireEvent.click(resetButton);

    expect(searchInput).toHaveValue('');
  });

  it('应该能够点击新增分组按钮', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} />
      </MockApp>
    );

    const addButton = screen.getByText('新增分组');
    fireEvent.click(addButton);

    // 验证新增功能被触发
    expect(addButton).toBeInTheDocument();
  });

  it('应该在visible为false时不显示Modal', () => {
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} visible={false} />
      </MockApp>
    );

    expect(screen.queryByText('分组管理')).not.toBeInTheDocument();
  });

  it('应该能够调用onCancel回调', () => {
    const onCancel = jest.fn();
    render(
      <MockApp>
        <GroupManagementModal {...defaultProps} onCancel={onCancel} />
      </MockApp>
    );

    // 点击Modal的关闭按钮
    const closeButton = document.querySelector('.ant-modal-close');
    if (closeButton) {
      fireEvent.click(closeButton);
      expect(onCancel).toHaveBeenCalled();
    }
  });
});
