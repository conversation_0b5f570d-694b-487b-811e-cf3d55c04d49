# 分页"跳至 xx 页"中文配置文档

## 问题描述

用户询问如何配置分页组件中"跳至 xx 页"的中文显示。这个功能涉及到 Ant Design 分页组件的快速跳转功能的本地化配置。

## 解决方案

### 🌐 1. 全局语言包自定义配置

**文件**: `src/App.tsx`

```typescript
import { App as AntdApp, ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import { router } from './router';
import zhCN from 'antd/locale/zh_CN';
import type { Locale } from 'antd/es/locale';

// 自定义中文配置，覆盖分页相关文本
const customZhCN: Locale = {
  ...zhCN,
  Pagination: {
    ...zhCN.Pagination,
    jump_to: '跳至',           // 🎯 "跳至" 文本
    jump_to_confirm: '确定',   // 🎯 跳转按钮文本
    page: '页',               // 🎯 "页" 文本
    prev_page: '上一页',
    next_page: '下一页',
    prev_5: '向前 5 页',
    next_5: '向后 5 页',
    prev_3: '向前 3 页',
    next_3: '向后 3 页',
    page_size: '条/页',
  },
};

function App() {
  return (
    <ConfigProvider
      locale={customZhCN}  // 🎯 使用自定义中文配置
      theme={{
        components: {
          Form: {
            itemMarginBottom: 0,
          },
        },
      }}
    >
      <AntdApp>
        <RouterProvider router={router} />
      </AntdApp>
    </ConfigProvider>
  );
}
```

### 📄 2. 组件级别的分页配置

**文件**: `src/components/GroupManagementModal.tsx`

```typescript
pagination={{
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: {
    goButton: '跳转'  // 🎯 自定义跳转按钮文本
  },
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  onChange: handleTableChange,
  onShowSizeChange: handleTableChange,
  pageSizeOptions: ['10', '20', '50', '100'],
  // 自定义分页按钮文本
  itemRender: (_current, type, originalElement) => {
    if (type === 'prev') {
      return <span>上一页</span>;
    }
    if (type === 'next') {
      return <span>下一页</span>;
    }
    if (type === 'jump-prev') {
      return <span>向前5页</span>;
    }
    if (type === 'jump-next') {
      return <span>向后5页</span>;
    }
    return originalElement;
  },
}}
```

## 配置说明

### 🔧 全局配置项说明

| 配置项            | 说明         | 默认值             | 自定义值    |
| ----------------- | ------------ | ------------------ | ----------- |
| `jump_to`         | "跳至"文本   | "Go to"            | "跳至"      |
| `jump_to_confirm` | 跳转确认按钮 | "Go"               | "确定"      |
| `page`            | "页"文本     | "page"             | "页"        |
| `prev_page`       | 上一页提示   | "Previous Page"    | "上一页"    |
| `next_page`       | 下一页提示   | "Next Page"        | "下一页"    |
| `prev_5`          | 向前5页提示  | "Previous 5 Pages" | "向前 5 页" |
| `next_5`          | 向后5页提示  | "Next 5 Pages"     | "向后 5 页" |
| `page_size`       | 页面大小文本 | "/ page"           | "条/页"     |

### 🎯 组件级配置项说明

| 配置项            | 说明           | 示例                                     |
| ----------------- | -------------- | ---------------------------------------- |
| `showQuickJumper` | 启用快速跳转   | `true` 或 `{ goButton: '跳转' }`         |
| `showTotal`       | 显示数据统计   | `(total, range) => '第 x-y 条，共 z 条'` |
| `itemRender`      | 自定义分页按钮 | 自定义上一页、下一页等按钮文本           |

## 实现效果

### 📱 界面显示效果

配置完成后，分页器将显示：

```
第 1-10 条，共 1000 条    [上一页] [1] [2] [3] ... [100] [下一页]    跳至 [___] 页 [跳转]    10 条/页 ▼
```

具体文本说明：

- **数据统计**: "第 1-10 条，共 1000 条"
- **导航按钮**: "上一页" / "下一页"
- **快速跳转**: "跳至 [输入框] 页 [跳转按钮]"
- **页面大小**: "10 条/页"

### 🔄 交互行为

1. **快速跳转输入框**: 用户可以输入页码
2. **跳转按钮**: 点击"跳转"按钮执行跳转
3. **页面大小选择**: 下拉选择每页显示条数
4. **导航按钮**: 点击上一页/下一页进行翻页

## 技术实现原理

### 1. 语言包继承机制

```typescript
const customZhCN: Locale = {
  ...zhCN, // 继承默认中文配置
  Pagination: {
    ...zhCN.Pagination, // 继承默认分页配置
    jump_to: '跳至', // 覆盖特定配置项
    // ... 其他自定义配置
  },
};
```

### 2. ConfigProvider 全局配置

```typescript
<ConfigProvider locale={customZhCN}>
  {/* 所有子组件都会使用这个语言配置 */}
</ConfigProvider>
```

### 3. 组件级别覆盖

```typescript
// 组件级别的配置会覆盖全局配置
showQuickJumper: {
  goButton: '跳转'; // 覆盖全局的 jump_to_confirm
}
```

## 配置优先级

1. **组件级配置** (最高优先级)
   - 直接在组件的 props 中配置
   - 如 `showQuickJumper: { goButton: '跳转' }`

2. **自定义语言包配置** (中等优先级)
   - 在 App.tsx 中的 customZhCN 配置
   - 覆盖默认语言包的配置

3. **默认语言包配置** (最低优先级)
   - Ant Design 内置的 zhCN 语言包
   - 提供基础的中文配置

## 测试验证

### 功能测试步骤

1. 访问 http://localhost:5174/task-table
2. 点击"分组管理"按钮打开Modal
3. 查看分页器底部的快速跳转区域
4. 验证显示文本为"跳至 [输入框] 页 [跳转]"
5. 输入页码并点击跳转按钮测试功能

### 预期结果

- ✅ 显示"跳至"而不是"Go to"
- ✅ 跳转按钮显示"跳转"而不是"Go"
- ✅ 输入框后显示"页"而不是"page"
- ✅ 功能正常，可以正确跳转到指定页面

## 扩展配置

### 其他可配置的分页文本

```typescript
const customZhCN: Locale = {
  ...zhCN,
  Pagination: {
    ...zhCN.Pagination,
    // 更多可配置项
    items_per_page: '条/页',
    jump_to: '跳至',
    jump_to_confirm: '确定',
    page: '页',
    prev_page: '上一页',
    next_page: '下一页',
    prev_5: '向前 5 页',
    next_5: '向后 5 页',
    prev_3: '向前 3 页',
    next_3: '向后 3 页',
    page_size: '页面大小',
  },
};
```

## 总结

通过全局语言包自定义配置和组件级别的配置相结合，成功实现了分页组件"跳至 xx 页"功能的完整中文化。这种配置方式既保证了全局一致性，又提供了组件级别的灵活性，为用户提供了完全本地化的分页体验。
