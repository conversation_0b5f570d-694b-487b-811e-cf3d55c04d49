// 引入React基础库和hooks
import React, { useState } from 'react';
// 引入ProTable组件
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

// 引入Ant Design组件
import { Button, Drawer, Form, Input, InputNumber, Space, Tag, Modal, Popconfirm, App, Select } from 'antd';
// 引入图标
import { useForm } from 'antd/es/form/Form';

/**
 * 数据项类型定义
 * 用于定义表格中每一行数据的类型
 */
type DataItem = {
  /**
   * 唯一标识符
   */
  id: string;
  /**
   * 姓名
   */
  name: string;
  /**
   * 年龄
   */
  age: number;
  /**
   * 地址
   */
  address: string;
  /**
   * 标签数组
   */
  tags: string[];
  /**
   * 创建时间
   */
  createTime: string;
};

/**
 * 查询参数类型定义
 * 用于定义API查询参数的类型
 */
type QueryParams = {
  /**
   * 姓名搜索条件（可选）
   */
  name?: string;
  /**
   * 地址搜索条件（可选）
   */
  address?: string;
  /**
   * 当前页码（可选，默认为1）
   */
  current?: number;
  /**
   * 每页条数（可选，默认为10）
   */
  pageSize?: number;
};

/**
 * 模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
const mockData: DataItem[] = [
  {
    id: '1',
    name: '张三',
    age: 32,
    address: '北京市朝阳区',
    tags: ['开发', '前端'],
    createTime: '2023-07-20 12:00:00',
  },
  {
    id: '2',
    name: '李四',
    age: 42,
    address: '上海市浦东新区',
    tags: ['设计师'],
    createTime: '2023-07-19 14:30:00',
  },
  {
    id: '3',
    name: '王五',
    age: 32,
    address: '广州市天河区',
    tags: ['后端', '运维'],
    createTime: '2023-07-18 09:15:00',
  },
  // 更多模拟数据...
  {
    id: '31',
    name: '王五',
    age: 32,
    address: '广州市天河区',
    tags: ['后端', '运维'],
    createTime: '2023-07-18 09:15:00',
  },
  {
    id: '32',
    name: '王五',
    age: 32,
    address: '广州市天河区',
    tags: ['后端', '运维'],
    createTime: '2023-07-18 09:15:00',
  },
];

/**
 * 模拟API调用 - 获取用户列表
 */
const getUsers = async (params: QueryParams) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  // 模拟分页和搜索逻辑
  let filteredData = [...mockData];

  // 根据搜索条件过滤数据
  if (params.name) {
    filteredData = filteredData.filter(item => item.name.includes(params.name!));
  }

  if (params.address) {
    filteredData = filteredData.filter(item => item.address.includes(params.address!));
  }

  // 分页处理
  const { current = 1, pageSize = 10 } = params;
  const startIndex = (current - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return {
    data: paginatedData,
    total: filteredData.length,
    success: true,
  };
};

/**
 * 模拟API调用 - 删除用户
 */
const deleteUser = async (id: string) => {
  console.log('删除用户:', id);
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  return true; // 模拟成功
};

/**
 * 模拟API调用 - 批量删除用户
 */
const batchDeleteUsers = async (ids: string[]) => {
  console.log('批量删除用户:', ids);
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 800));
  return true; // 模拟成功
};

/**
 * ProTable 示例组件
 * 展示如何使用 ProTable 实现数据表格功能
 */
const ProTableDemo: React.FC = () => {
  // 使用 App hooks 获取 message 实例
  const { message } = App.useApp();
  // 表格引用，用于手动刷新表格
  const actionRef = React.useRef<ActionType>(null);
  // 控制抽屉显示/隐藏
  const [drawerVisible, setDrawerVisible] = useState(false);
  // 表单实例
  const [form] = useForm<DataItem>();
  // 控制关闭确认弹窗
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  // 控制批量删除确认弹窗
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState(false);
  // 多选相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // 选中的行数据
  const [selectedRows, setSelectedRows] = useState<DataItem[]>([]);
  // 控制添加用户Modal显示/隐藏
  const [addUserVisible, setAddUserVisible] = useState(false);
  // 添加用户表单实例
  const [addForm] = useForm<Omit<DataItem, 'id' | 'createTime'>>();

  /**
   * 处理多选变化（支持跨页选择）
   */
  const handleSelectionChange = (keys: React.Key[], rows: DataItem[]) => {
    setSelectedRowKeys(keys);
    setSelectedRows(rows);
    console.log('选中的行数据:', rows);
  };

  /**
   * 取消所有选择
   */
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  /**
   * 打开批量删除确认弹窗
   */
  const showBatchDeleteModal = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的用户');
      return;
    }
    setShowBatchDeleteConfirm(true);
  };

  /**
   * 处理批量删除操作
   */
  const handleBatchDelete = async () => {
    try {
      // 调用批量删除API
      const success = await batchDeleteUsers(selectedRowKeys as string[]);

      if (success) {
        message.success(`成功删除 ${selectedRowKeys.length} 个用户`);
        // 关闭确认弹窗
        setShowBatchDeleteConfirm(false);
        // 清除多选状态
        handleClearSelection();
        // 重新加载表格数据
        actionRef.current?.reload();
      } else {
        throw new Error('批量删除失败');
      }
    } catch (error) {
      message.error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      // 关闭确认弹窗
      setShowBatchDeleteConfirm(false);
    }
  };

  /**
   * 取消批量删除
   */
  const cancelBatchDelete = () => {
    setShowBatchDeleteConfirm(false);
  };

  /**
   * 处理编辑操作
   * @param record 当前行数据
   */
  const handleEdit = (record: DataItem) => {
    // 设置表单字段值，只设置表单中存在的字段
    const formValues = {
      name: record.name,
      age: record.age,
      address: record.address,
    };
    form.setFieldsValue(formValues);
    // 打开抽屉
    setDrawerVisible(true);
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单数据:', values);
      // 这里可以调用更新API
      message.success('保存成功');
      // 关闭抽屉
      setDrawerVisible(false);
      // 刷新表格
      // actionRef.current?.reload();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  /**
   * 重置表单
   */
  const handleReset = () => {
    form.resetFields();
  };

  /**
   * 处理抽屉关闭
   */
  const handleDrawerClose = () => {
    // 检查表单是否被修改过
    if (form.isFieldsTouched()) {
      setShowCloseConfirm(true);
    } else {
      setDrawerVisible(false);
    }
  };

  /**
   * 确认关闭抽屉
   */
  const confirmClose = () => {
    form.resetFields();
    setDrawerVisible(false);
    setShowCloseConfirm(false);
  };

  /**
   * 取消关闭抽屉
   */
  const cancelClose = () => {
    setShowCloseConfirm(false);
  };

  /**
   * 处理删除操作
   * @param record 当前行数据
   */
  const handleDelete = async (record: DataItem) => {
    try {
      const success = await deleteUser(record.id);
      if (success) {
        message.success(`用户 ${record.name} 删除成功`);
        // 重新加载表格数据
        actionRef.current?.reload();
      } else {
        throw new Error('删除失败');
      }
    } catch (error) {
      message.error(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  /**
   * 处理添加用户
   */
  const handleAddUser = () => {
    setAddUserVisible(true);
    addForm.resetFields();
  };

  /**
   * 处理添加用户提交
   */
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();
      console.log('添加用户数据:', values);

      // 模拟添加用户API调用
      const newUser: DataItem = {
        id: Date.now().toString(),
        ...values,
        createTime: new Date().toLocaleString(),
      };

      message.success(`用户 ${newUser.name} 添加成功`);
      setAddUserVisible(false);
      addForm.resetFields();
      // 重新加载表格数据
      actionRef.current?.reload();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  /**
   * 处理添加用户取消
   */
  const handleAddCancel = () => {
    // 检查表单是否被修改过
    if (addForm.isFieldsTouched()) {
      Modal.confirm({
        title: '确认取消',
        content: '表单内容未保存，确定要取消吗？',
        onOk: () => {
          addForm.resetFields();
          setAddUserVisible(false);
        },
      });
    } else {
      setAddUserVisible(false);
    }
  };

  /**
   * 表格列定义
   */
  const columns: ProColumns<DataItem>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      ellipsis: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入姓名',
          },
        ],
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      valueType: 'digit',
      renderFormItem: () => {
        return (
          <Space.Compact style={{ width: '100%' }}>
            <InputNumber style={{ width: '45%' }} placeholder='最小年龄' min={0} max={150} />
            <Input
              style={{
                width: '10%',
                borderLeft: 0,
                borderRight: 0,
                pointerEvents: 'none',
              }}
              placeholder='~'
              disabled
            />
            <InputNumber style={{ width: '45%' }} placeholder='最大年龄' min={0} max={150} />
          </Space.Compact>
        );
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      hideInSearch: true,
      render: (_, record) => (
        <Space size={[0, 8]} wrap>
          {record.tags?.map(tag => (
            <Tag key={tag} color={tag === '开发' ? 'blue' : tag === '前端' ? 'green' : tag === '后端' ? 'orange' : tag === '设计师' ? 'purple' : 'default'}>
              {tag}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => (
        <Space size='middle'>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <Popconfirm
            title='确定要删除吗？'
            description={`确定要删除用户 "${record.name}" 吗？此操作不可恢复。`}
            onConfirm={() => handleDelete(record)}
            okText='确定'
            cancelText='取消'
            okType='danger'
          >
            <a style={{ color: 'red' }}>删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 选择提示 */}
      {selectedRowKeys.length > 0 && (
        <div
          style={{
            padding: '12px 16px',
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '6px',
            marginBottom: '16px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <span
              style={{
                color: '#1890ff',
                fontWeight: 500,
              }}
            >
              已选择 {selectedRowKeys.length} 项
            </span>
            <Button
              type='link'
              size='small'
              onClick={handleClearSelection}
              style={{
                padding: '0 8px',
                height: 'auto',
              }}
            >
              清除选择
            </Button>
          </div>
          <Space>
            <Button type='primary' danger size='small' onClick={showBatchDeleteModal} disabled={selectedRowKeys.length === 0}>
              批量删除
            </Button>
          </Space>
        </div>
      )}

      {/* ProTable */}
      <ProTable<DataItem>
        columns={columns}
        actionRef={actionRef}
        request={getUsers}
        rowKey='id'
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        dateFormatter='string'
        headerTitle='用户管理'
        toolBarRender={() => [
          <Button key='add' type='primary' onClick={handleAddUser}>
            添加用户
          </Button>,
        ]}
        rowSelection={{
          selectedRowKeys,
          onChange: handleSelectionChange,
          preserveSelectedRowKeys: true,
        }}
        scroll={{ x: 1000 }}
      />

      {/* 编辑抽屉 */}
      <Drawer
        title='编辑用户信息'
        width={600}
        onClose={handleDrawerClose}
        open={drawerVisible}
        styles={{
          body: { paddingBottom: 80 },
        }}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={handleDrawerClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleReset} style={{ marginRight: 8 }}>
              重置
            </Button>
            <Button onClick={handleSubmit} type='primary'>
              保存
            </Button>
          </div>
        }
      >
        <Form form={form} layout='vertical'>
          <Form.Item
            name='name'
            label='姓名'
            rules={[
              {
                required: true,
                message: '请输入姓名',
              },
            ]}
          >
            <Input placeholder='请输入姓名' />
          </Form.Item>
          <Form.Item
            name='age'
            label='年龄'
            rules={[
              {
                required: true,
                message: '请输入年龄',
              },
            ]}
          >
            <InputNumber min={1} max={150} style={{ width: '100%' }} placeholder='请输入年龄' />
          </Form.Item>
          <Form.Item
            name='address'
            label='地址'
            rules={[
              {
                required: true,
                message: '请输入地址',
              },
            ]}
          >
            <Input.TextArea rows={3} placeholder='请输入地址' />
          </Form.Item>
        </Form>
      </Drawer>

      {/* 关闭确认弹窗 */}
      <Modal title='确认关闭' open={showCloseConfirm} onOk={confirmClose} onCancel={cancelClose} okText='确定' cancelText='取消' okType='danger'>
        <p>表单内容未保存，确定要关闭吗？</p>
      </Modal>

      {/* 批量删除确认弹窗 */}
      <Modal title='批量删除确认' open={showBatchDeleteConfirm} onOk={handleBatchDelete} onCancel={cancelBatchDelete} okText='确定删除' cancelText='取消' okType='danger' width={400}>
        <div style={{ padding: '16px 0' }}>
          <p
            style={{
              marginBottom: '12px',
              fontSize: '14px',
            }}
          >
            您即将删除以下{' '}
            <strong
              style={{
                color: '#ff4d4f',
              }}
            >
              {selectedRowKeys.length}
            </strong>{' '}
            个用户：
          </p>
          <div
            style={{
              maxHeight: '200px',
              overflowY: 'auto',
              backgroundColor: '#fafafa',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #d9d9d9',
            }}
          >
            {selectedRows.map((user, index) => (
              <div
                key={index}
                style={{
                  padding: '2px 0',
                }}
              >
                • {user.name}
              </div>
            ))}
          </div>
          <p
            style={{
              marginTop: '12px',
              color: '#ff4d4f',
              fontSize: '12px',
            }}
          >
            此操作不可恢复，请谨慎操作！
          </p>
        </div>
      </Modal>

      {/* 添加用户Modal */}
      <Modal title='添加用户' open={addUserVisible} onOk={handleAddSubmit} onCancel={handleAddCancel} okText='确定' cancelText='取消' width={600} destroyOnHidden>
        <Form form={addForm} layout='vertical' style={{ marginTop: 16 }}>
          <Form.Item
            name='name'
            label='姓名'
            rules={[
              {
                required: true,
                message: '请输入姓名',
              },
            ]}
          >
            <Input placeholder='请输入姓名' />
          </Form.Item>
          <Form.Item
            name='age'
            label='年龄'
            rules={[
              {
                required: true,
                message: '请输入年龄',
              },
            ]}
          >
            <InputNumber min={1} max={150} style={{ width: '100%' }} placeholder='请输入年龄' />
          </Form.Item>
          <Form.Item
            name='address'
            label='地址'
            rules={[
              {
                required: true,
                message: '请输入地址',
              },
            ]}
          >
            <Input.TextArea rows={3} placeholder='请输入地址' />
          </Form.Item>
          <Form.Item name='tags' label='标签'>
            <Select
              mode='multiple'
              placeholder='请选择标签'
              options={[
                {
                  label: '开发',
                  value: '开发',
                },
                {
                  label: '前端',
                  value: '前端',
                },
                {
                  label: '后端',
                  value: '后端',
                },
                {
                  label: '设计师',
                  value: '设计师',
                },
                {
                  label: '运维',
                  value: '运维',
                },
                {
                  label: '测试',
                  value: '测试',
                },
              ]}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProTableDemo;
